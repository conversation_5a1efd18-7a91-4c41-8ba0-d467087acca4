import os
import json
import cv2
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from pathlib import Path

def load_frame_data(json_path):
    """加载frame_data.json文件"""
    with open(json_path, 'r', encoding='utf-8') as f:
        return json.load(f)

def get_bounding_boxes(frame_data):
    """从frame_data中提取边界框信息"""
    boxes = []
    for capture in frame_data.get('captures', []):
        for annotation in capture.get('annotations', []):
            if annotation.get('@type') == 'type.unity.com/unity.solo.BoundingBox2DAnnotation':
                for value in annotation.get('values', []):
                    box_info = {
                        'label_id': value.get('labelId'),
                        'label_name': value.get('labelName'),
                        'origin': value.get('origin'),
                        'dimension': value.get('dimension')
                    }
                    boxes.append(box_info)
    return boxes

def unity_to_yolo_format(origin, dimension, img_width, img_height):
    """将Unity格式转换为YOLO格式"""
    x_left, y_top = origin
    width, height = dimension
    
    # 计算中心点
    x_center = x_left + width / 2
    y_center = y_top + height / 2
    
    # 转换为相对坐标
    x_center_rel = x_center / img_width
    y_center_rel = y_center / img_height
    width_rel = width / img_width
    height_rel = height / img_height
    
    return x_center_rel, y_center_rel, width_rel, height_rel

def visualize_annotations(image_path, frame_data_path, output_dir=None):
    """可视化单张图片的标注"""
    # 加载图片
    image = cv2.imread(image_path)
    if image is None:
        print(f"无法加载图片: {image_path}")
        return
    
    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    img_height, img_width = image.shape[:2]
    
    # 加载标注数据
    frame_data = load_frame_data(frame_data_path)
    boxes = get_bounding_boxes(frame_data)
    
    # 创建图形
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # 左侧：原始Unity格式可视化
    ax1.imshow(image)
    ax1.set_title('Unity格式标注')
    ax1.axis('off')
    
    for box in boxes:
        origin = box['origin']
        dimension = box['dimension']
        label_name = box['label_name']
        
        rect = patches.Rectangle(
            origin, dimension[0], dimension[1],
            linewidth=2, edgecolor='red', facecolor='none'
        )
        ax1.add_patch(rect)
        ax1.text(origin[0], origin[1]-10, label_name, 
                color='red', fontsize=12, fontweight='bold')
    
    # 右侧：YOLO格式可视化
    ax2.imshow(image)
    ax2.set_title('YOLO格式标注')
    ax2.axis('off')
    
    for box in boxes:
        origin = box['origin']
        dimension = box['dimension']
        label_name = box['label_name']
        
        # 转换为YOLO格式
        x_center_rel, y_center_rel, width_rel, height_rel = unity_to_yolo_format(
            origin, dimension, img_width, img_height
        )
        
        # 转换回绝对坐标用于显示
        x_center = x_center_rel * img_width
        y_center = y_center_rel * img_height
        width = width_rel * img_width
        height = height_rel * img_height
        
        x_left = x_center - width / 2
        y_top = y_center - height / 2
        
        rect = patches.Rectangle(
            (x_left, y_top), width, height,
            linewidth=2, edgecolor='blue', facecolor='none'
        )
        ax2.add_patch(rect)
        ax2.text(x_left, y_top-10, 
                f'{label_name}\nYOLO: ({x_center_rel:.3f}, {y_center_rel:.3f}, {width_rel:.3f}, {height_rel:.3f})',
                color='blue', fontsize=10, fontweight='bold')
    
    plt.tight_layout()
    
    if output_dir:
        os.makedirs(output_dir, exist_ok=True)
        filename = Path(image_path).stem + '_annotated.png'
        plt.savefig(os.path.join(output_dir, filename), dpi=150, bbox_inches='tight')
        print(f"保存可视化结果到: {os.path.join(output_dir, filename)}")
    
    plt.show()

def batch_visualize(data_dir, output_dir=None, max_images=10):
    """批量可视化数据集"""
    sequence_dir = os.path.join(data_dir, 'sequence.0')
    if not os.path.exists(sequence_dir):
        print(f"未找到sequence.0目录: {sequence_dir}")
        return
    
    # 获取所有图片文件
    image_files = [f for f in os.listdir(sequence_dir) if f.endswith('.camera.png')]
    image_files.sort()
    
    if output_dir:
        os.makedirs(output_dir, exist_ok=True)
    
    count = 0
    for image_file in image_files:
        if count >= max_images:
            break
            
        step_name = image_file.replace('.camera.png', '')
        image_path = os.path.join(sequence_dir, image_file)
        frame_data_path = os.path.join(sequence_dir, f'{step_name}.frame_data.json')
        
        if os.path.exists(frame_data_path):
            print(f"处理: {image_file}")
            visualize_annotations(image_path, frame_data_path, output_dir)
            count += 1
        else:
            print(f"未找到对应的标注文件: {frame_data_path}")

def generate_yolo_labels(data_dir, output_dir):
    """生成YOLO格式的标签文件"""
    sequence_dir = os.path.join(data_dir, 'sequence.0')
    os.makedirs(output_dir, exist_ok=True)
    
    # 创建类别映射文件
    with open(os.path.join(data_dir, 'annotation_definitions.json'), 'r', encoding='utf-8') as f:
        ann_def = json.load(f)
    
    class_mapping = {}
    classes_yaml = []
    
    for definition in ann_def['annotationDefinitions']:
        for spec in definition.get('spec', []):
            label_id = spec['label_id']
            label_name = spec['label_name']
            # YOLO类别从0开始，Unity可能从1开始
            yolo_class_id = label_id - 1 if label_id > 0 else label_id
            class_mapping[label_id] = yolo_class_id
            classes_yaml.append(label_name)
    
    # 保存类别文件
    with open(os.path.join(output_dir, 'classes.txt'), 'w', encoding='utf-8') as f:
        for class_name in classes_yaml:
            f.write(f"{class_name}\n")
    
    print(f"类别映射: {class_mapping}")
    print(f"类别列表: {classes_yaml}")
    
    # 处理每张图片
    image_files = [f for f in os.listdir(sequence_dir) if f.endswith('.camera.png')]
    
    for image_file in image_files:
        step_name = image_file.replace('.camera.png', '')
        image_path = os.path.join(sequence_dir, image_file)
        frame_data_path = os.path.join(sequence_dir, f'{step_name}.frame_data.json')
        
        if not os.path.exists(frame_data_path):
            continue
        
        # 获取图片尺寸
        image = cv2.imread(image_path)
        if image is None:
            continue
        img_height, img_width = image.shape[:2]
        
        # 加载标注数据
        frame_data = load_frame_data(frame_data_path)
        boxes = get_bounding_boxes(frame_data)
        
        # 生成YOLO标签文件
        label_file = os.path.join(output_dir, f'{step_name}.txt')
        with open(label_file, 'w') as f:
            for box in boxes:
                label_id = box['label_id']
                origin = box['origin']
                dimension = box['dimension']
                
                # 转换为YOLO格式
                x_center_rel, y_center_rel, width_rel, height_rel = unity_to_yolo_format(
                    origin, dimension, img_width, img_height
                )
                
                yolo_class_id = class_mapping.get(label_id, 0)
                f.write(f"{yolo_class_id} {x_center_rel:.6f} {y_center_rel:.6f} {width_rel:.6f} {height_rel:.6f}\n")
        
        print(f"生成YOLO标签: {label_file}")

def main():
    """主函数"""
    data_dir = r"C:\Users\<USER>\Desktop\solo"
    output_dir = r"C:\Users\<USER>\Desktop\solo\visualizations"
    yolo_output_dir = r"C:\Users\<USER>\Desktop\solo\yolo_labels"
    
    print("=== 数据集可视化工具 ===")
    print(f"数据目录: {data_dir}")
    print(f"可视化输出目录: {output_dir}")
    print(f"YOLO标签输出目录: {yolo_output_dir}")
    
    # 选择操作
    while True:
        print("\n请选择操作:")
        print("1. 可视化单张图片")
        print("2. 批量可视化(前10张)")
        print("3. 生成YOLO格式标签文件")
        print("4. 退出")
        
        choice = input("请输入选择 (1-4): ").strip()
        
        if choice == '1':
            # 列出可用图片
            sequence_dir = os.path.join(data_dir, 'sequence.0')
            image_files = [f for f in os.listdir(sequence_dir) if f.endswith('.camera.png')]
            image_files.sort()
            
            print(f"可用图片 (共{len(image_files)}张):")
            for i, img in enumerate(image_files[:10]):
                print(f"{i}: {img}")
            if len(image_files) > 10:
                print("...")
                
            try:
                idx = int(input("请输入图片索引: "))
                if 0 <= idx < len(image_files):
                    image_file = image_files[idx]
                    step_name = image_file.replace('.camera.png', '')
                    image_path = os.path.join(sequence_dir, image_file)
                    frame_data_path = os.path.join(sequence_dir, f'{step_name}.frame_data.json')
                    visualize_annotations(image_path, frame_data_path, output_dir)
                else:
                    print("无效的索引")
            except ValueError:
                print("请输入有效的数字")
                
        elif choice == '2':
            batch_visualize(data_dir, output_dir, max_images=10)
            
        elif choice == '3':
            generate_yolo_labels(data_dir, yolo_output_dir)
            print("YOLO标签文件生成完成！")
            
        elif choice == '4':
            break
            
        else:
            print("无效的选择，请重新输入")

if __name__ == "__main__":
    main()